from django.db import models


class ChargeDemandInfo(models.Model):
    contract_num = models.CharField(max_length=32, blank=True, null=True)
    customer_invoice_name = models.CharField(max_length=64, blank=True, null=True)
    customer_deposit_bank = models.Char<PERSON>ield(max_length=128, blank=True, null=True)
    customer_deposit_bank_sub = models.CharField(max_length=128, blank=True, null=True)
    customer_bank_account_name = models.CharField(max_length=128, blank=True, null=True)
    customer_bank_account = models.CharField(max_length=128, blank=True, null=True)
    customer_tax_number = models.Char<PERSON>ield(max_length=128, blank=True, null=True)
    customer_payment_type = models.Char<PERSON>ield(max_length=32, blank=True, null=True)
    customer_receive_explain = models.CharField(max_length=512, blank=True, null=True)
    customer_verify_type = models.CharField(max_length=32, blank=True, null=True)
    customer_fare_order = models.Char<PERSON><PERSON>(max_length=32, blank=True, null=True)
    is_unusual_need = models.Char<PERSON>ield(max_length=32, blank=True, null=True)
    unusual_need_explain = models.Char<PERSON>ield(max_length=512, blank=True, null=True)
    is_open_charge = models.CharField(max_length=32, blank=True, null=True)
    charge_num = models.CharField(max_length=32, blank=True, null=True)
    sub_charge_num = models.CharField(max_length=32, blank=True, null=True)
    charge_start_day = models.DateField(blank=True, null=True)
    charge_end_day = models.DateField(blank=True, null=True)
    charge_status = models.CharField(max_length=32, blank=True, null=True)
    up_user = models.CharField(max_length=32, blank=True, null=True)
    up_time = models.DateTimeField(blank=True, null=True)
    postal_type = models.CharField(max_length=32, blank=True, null=True)
    postal_address = models.CharField(max_length=256, blank=True, null=True)

    class Meta:
        verbose_name = "账务需求表"
        db_table = "charge_demand_info"
        ordering = ("-up_time",)

    def __str__(self):
        return f"<ChargeDemand: {self.charge_num}>"
