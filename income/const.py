from django.db import models

SUPER_ADMIN = "admin"


class State(models.TextChoices):
    VALID = "U", "有效"
    INVALID = "E", "无效"


class MenuIdentify(models.TextChoices):
    """菜单唯一标识"""

    USER_INFO = "1001", "用户角色"
    USER_ROLE = "1002", "部门"
    USER_DEPARTMENT = "1003", "菜单管理"

    CUSTOMER_INFO = "2001", "客户信息"
    CUSTOMER_APPROVE = "2002", "客户审批"
    ACCOUNT_SEQ = "2003", "分账序号"

    CONTRACT_INFO = "3001", "合同信息录入"

    INCOME_ORDER = "4001", "收入订单录入"
    INCOME_FEE_TEMPLATE = "4002", "费用模板"
    INCOME_FEE_PACKAGE = "4003", "费用套餐"

    INCOME_CHARGE = "6002", "收入权责"
    INCOME_ADJUST = "6003", "收入调账"

    INCOME_RECEIPT = "7001", "对账信息"

    INVOICE = "8001", "发票开具"
    BILLING = "8002", "账单管理"


# 正则
PHONE_RE = r"^(0[1-9]\d{1,2})-?(\d{7,8})$"
EMAIL_RE = r"^[a-zA-Z0-9_.%+-]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$"
MOBILE_PHONE_RE = r"^1[3-9]\d{9}$"


class UserState(models.IntegerChoices):
    """用户状态"""

    DISABLE = 0, "禁用"
    ENABLE = 1, "启用"
    FREEZE = 2, "冻结"
    OFF = 3, "注销"


# 发票信息
# -------------------------------------------------------------------------------------
class InvoiceType(models.TextChoices):
    """增值税专票、增值税专票、invoice"""

    VAT_SPECIAL_TICKET = "增值税专票", "增值税专票"
    VAT_GENERAL_TICKET = "增值税普票", "增值税普票"
    INVOICE = "invoice", "invoice"


class CustomerVerifyType(models.TextChoices):
    """客户对账类型"""

    NO_VERIFY = "不对账", "不对账"
    VERIFY_NO_CONFIRM = "对账无需确认", "对账无需确认"
    VERIFY_EXPIRED_NO_CONFIRM = "对账超期无需确认", "对账超期无需确认"
    VERIFY_NEED_CONFIRM = "对账要确认", "对账要确认"


class CustomerFareOrder(models.TextChoices):
    """客户票款先后"""

    TICKET_FIRST = "先票后款", "先票后款"
    PAYMENT_FIRST = "先款后票", "先款后票"


class IsUnusualNeed(models.TextChoices):
    """是否特殊开账要求"""

    YES = "是", "是"
    NO = "否", "否"


class IsOpenCharge(models.TextChoices):
    """是否开账"""

    SUSPENDED = "暂停", "暂停"
    ACTIVE = "开账", "开账"
    TERMINATED = "终止", "终止"


class PostalType(models.TextChoices):
    """邮递方式"""

    EMAIL = "邮件", "邮件"
    EXPRESS = "快递", "快递"
    EMAIL_AND_EXPRESS = "邮件且快递", "邮件且快递"
    NONE = "无需", "无需"


# 订单信息
# -------------------------------------------------------------------------------------
class OrderServiceStatus(models.TextChoices):
    """新装服务状态"""

    DRAFT = "新装暂存", "新装暂存"
    ORDER_REVIEW = "新装下单审核", "新装下单审核"
    DISPATCHED = "新装派单", "新装派单"
    IMPLEMENTATION = "新装实施", "新装实施"
    IN_SERVICE = "服务中", "服务中"

    DECOM_ROLLBACK = "拆机回退", "拆机回退"
    DECOM_ORDER_REVIEW = "拆机下单审核", "拆机下单审核"
    DECOM_DISPATCHED = "拆机派单", "拆机派单"
    DECOM_IMPLEMENTATION = "拆机实施", "拆机实施"
    DECOM_MAINTENANCE_REVIEW = "拆机维护信息复核", "拆机维护信息复核"
    DECOM_SERVICE_TERMINATED = "服务终止", "服务终止"

    CHG_DECOM_ROLLBACK = "变更拆机回退", "变更拆机回退"
    CHG_DECOM_ORDER_REVIEW = "变更拆机下单审核", "变更拆机下单审核"
    CHG_DECOM_DISPATCHED = "变更拆机派单", "变更拆机派单"
    CHG_DECOM_IMPLEMENTATION = "变更拆机实施", "变更拆机实施"
    CHG_DECOM_MAINTENANCE_REVIEW = "变更拆机维护信息复核", "变更拆机维护信息复核"
    CHG_DECOM_SERVICE_TERMINATED = "变更服务终止", "变更服务终止"


class OrderBillStatus(models.TextChoices):
    """计费状态"""

    INCOMPLETE = "未完工", "未完工"
    BILLING_CONFIRMED = "新装计费确认", "新装计费确认"
    FINANCIAL_CONFIRMED = "新装账务确认", "新装账务确认"
    IN_PROGRESS = "计费中", "计费中"

    DECOM_BILLING_CONFIRMED = "拆机计费确认", "拆机计费确认"
    DECOM_FINANCIAL_CONFIRMED = "拆机账务确认", "拆机账务确认"
    TERMINATED = "计费终止", "计费终止"

    CHG_DECOM_BILLING_CONFIRMED = "变更拆机计费确认", "变更拆机计费确认"
    CHG_DECOM_FINANCIAL_CONFIRMED = "变更拆机账务确认", "变更拆机账务确认"
    CHG_TERMINATED = "变更计费终止", "变更计费终止"


class OrderType(models.TextChoices):
    """订单类型"""

    FORMAL = "客户正式", "客户正式"
    TEST = "客户测试", "客户测试"
    SELF_USE = "自用", "自用"
    CANCELED = "注销", "注销"


class OrderClass(models.IntegerChoices):
    """订单种类"""

    NORMAL = 0, "普通"
    VOICE = 1, "语音订单"
    TRAFFIC = 2, "流量订单"


class JobStatus(models.TextChoices):
    """派工状态"""

    NOT_DISPATCHED = "未派工", "未派工"
    DISPATCHED = "已派工", "已派工"
    DISPATCH_CANCELED = "派工撤销", "派工撤销"


class ServiceType(models.TextChoices):
    """服务类型"""

    NEW = "新增", "新增"
    CHANGE = "变更", "变更"
    RENEWAL = "续约", "续约"


class PayCycle(models.TextChoices):
    """付费周期"""

    MONTHLY = "月", "月"
    QUARTERLY = "季度", "季度"
    YEARLY = "年", "年"


class PayType(models.TextChoices):
    """付费方式"""

    PREPAID = "预付", "预付"
    POSTPAID = "后付", "后付"
    CURRENT_MONTH = "当月付", "当月付"


# 创建订单时只读字段
ORDER_READ_ONLY_FIELDS = (
    "service_status",
    "bill_status",
    "job_status",
    "total_num",
    "reality_bill_start_date",
    "new_build_bill_time",
    "new_build_charge_time",
    "new_build_support_time",
    "remove_required_finished_date",
    "reality_bill_end_date",
    "remove_build_start_time",
    "remove_build_finished_time",
    "remove_build_bill_time",
    "remove_build_charge_time",
    "remove_build_support_time",
    "group_approve_state",
    "create_user",
    "created_at",
)


# 客户信息
# -------------------------------------------------------------------------------------
class CustomerClass(models.TextChoices):
    """客户类别"""

    CONTRACT = "合同客户", "合同客户"
    NOMINAL = "名义客户", "名义客户"


class CustomerType(models.TextChoices):
    """客户类型"""

    DIRECT_DOMESTIC = "直客-国内", "直客-国内"
    DIRECT_OVERSEAS = "直客海外", "直客海外"
    CHANNEL_DOMESTIC = "渠道-国内", "渠道-国内"
    CHANNEL_OVERSEAS = "渠道-海外", "渠道-海外"


class TradeType(models.TextChoices):
    """行业类型"""

    TRADITIONAL = "传统企业", "传统企业"
    INTERNET = "互联网企业", "互联网企业"
    XAAS = "XaaS", "XaaS"
    TELECOM = "电信运营商", "电信运营商"
    RESELLER = "转售商", "转售商"


class CustomerState(models.TextChoices):
    """状态"""

    INQUIRY = "查询", "查询"
    ACTIVE = "正式", "正式"
    LOST = "流失", "流失"
    INACTIVE = "失效", "失效"


class ApproveState(models.TextChoices):
    """审核操作"""

    INIT = "init", "初始"
    CONFIRM = "confirm", "提交审核"
    REVOKE = "revoke", "撤回"
    ACTIVE = "active", "审核确认"
    BACK = "back", "退回"


class ApproveAction(models.TextChoices):
    """审批动作"""

    SUBMIT = "submit", "提交审核"
    REVOKE = "revoke", "撤回"
    APPROVE = "approve", "审核通过"
    REJECT = "reject", "驳回"


APPROVE_MAP = {
    ApproveAction.SUBMIT: ApproveState.CONFIRM,
    ApproveAction.REVOKE: ApproveState.REVOKE,
    ApproveAction.APPROVE: ApproveState.ACTIVE,
    ApproveAction.REJECT: ApproveState.BACK,
}


# 客户联系人信息
# -------------------------------------------------------------------------------------
class ContactClass(models.TextChoices):
    """关联类型"""

    CUSTOMER = "客户", "客户"
    CONTRACT = "合同", "合同"
    FINANCIAL = "账务", "账务"


class ContactType(models.TextChoices):
    """联系人类型"""

    BUSINESS = "商务", "商务"
    PURCHASE = "采购", "采购"
    FINANCIAL = "账务", "账务"
    SERVICE = "服务", "服务"
    OTHER = "其他", "其他"


class OwnType(models.TextChoices):
    """归属类型"""

    CUSTOMER = "客户", "客户"
    SUPPLIER = "供应商", "供应商"
    CHANNEL = "合作渠道", "合作渠道"


# 费用
# -------------------------------------------------------------------------------------
class OwnBusiness(models.IntegerChoices):
    """业务类型"""

    NORMAL = 0, "普通"
    VOICE = 1, "语音"
    TRAFFIC = 2, "流量"


# 合同
# -------------------------------------------------------------------------------------
class ContractTerm(models.IntegerChoices):
    """合同期限"""

    FIXED = 0, "固定期限合同"
    INDEFINITE = 1, "无固定期限合同"


# 出账
# -------------------------------------------------------------------------------------
FIELD_MAPPING = {
    "batch_no": "批次号",
    "order_no": "订单号",
    "sub_order_no": "子订单",
    "charge_month": "权责账期",
    "order_charge_begin": "订单开始时间",
    "order_charge_end": "订单结束时间",
    "tax_type": "税率类型",
    "fee_amount": "出账金额",
    "pay_amount": "已核销金额",
    "unpay_amount": "未核销金额",
    "tax": "税率",
    "fee_type": "计费类型",
    "pay_type": "付费类型",
    "currency_type": "币种",
    "income_type": "收入分类",
    "speed": "速率",
    "charge_explain": "资费说明",
    "a_info": "A端信息",
    "product_main_category": "产品主类",
    "product_sub_category": "产品次类",
    "order_type": "订单类型",
    "bill_template": "出账模版",
    "contract_num": "合同编号",
    "current_attr": "当年业务属性",
    "customer_num": "客户编号",
    "account_seq": "分账序号",
    "charge_user": "操作员",
    "charge_type": "出账类型",
    "created_at": "创建时间",
    "customer_name": "客户名称",
    "sale_name": "销售名称",
    "sign_contract_entity": "签约方",
    "contract_legal_num": "合同法务编号",
    "adjust_month": "调整账期",
    "adjust_reason_class": "调账分类",
    "adjust_reason": "调账原因",
}


class ChargeType(models.IntegerChoices):
    """出账类型"""

    SYSTEM = 1, "系统出账"
    ADJUSTMENT = 2, "调账"
    TAX_ADJUSTMENT = 3, "调税"
    EXTERNAL_IMPORT = 4, "外部费用导入"


# 调账
# -------------------------------------------------------------------------------------
class AdjustType(models.IntegerChoices):
    """调账类别"""

    ACCRUAL = 1, "权责调账"
    NON_ACCRUAL = 2, "无权责调账"
    EXTERNAL_IMPORT = 3, "外部费用导入调账"


class AdjustState(models.IntegerChoices):
    """调账状态 0-初始化导入, 1-审批通过, 2-审批拒绝"""

    INITIAL_IMPORT = 0, "初始化导入"
    APPROVED = 1, "审批通过"
    REJECTED = 2, "审批拒绝"


class AdjustApproveAction(models.TextChoices):
    """审批动作"""

    APPROVE = "approve", "审核通过"
    REJECT = "reject", "审批拒绝"


ADJUST_FIELD_MAPPING = {
    "batch_no": "批次号",
    "order_no": "订单号",
    "sub_order_no": "子订单",
    "charge_month": "权责账期",
    "adjust_month": "调整账期",
    "adjust_amount": "调整金额",
    "adjust_tax": "调整税额",
    "adjust_reason_class": "调账分类",
    "adjust_reason": "调账原因",
    "state": "状态",
    "adjust_type": "调账类别",
    "create_user": "操作员",
    "created_at": "创建时间",
}


# 对账
# -------------------------------------------------------------------------------------
class ReceiptStatus(models.IntegerChoices):
    """流水状态"""

    PENDING_CLAIM = 0, "待认款"
    PENDING_CONFIRMATION = 1, "待确认"
    CONFIRMED = 2, "已认款"


class ReceiveType(models.TextChoices):
    """核销/收款状态"""

    WRITE_OFF = "核销", "核销"
    PAYMENT_RECEIVED = "收款", "收款"


class GroupApproveState(models.IntegerChoices):
    """集团审批状态"""

    INIT = 0, "init"
    PENDING = 1, "pending"
    APPROVED = 2, "approved"
    REJECTED = 3, "rejected"
    CLOSED = 4, "closed"


class UnConfirmedApproveState(models.IntegerChoices):
    """未确认的审批状态"""

    INIT = 0, "init"
    PENDING = 1, "pending"
    REJECTED = 3, "rejected"


# 发票
# -------------------------------------------------------------------------------------
class InvoiceTargetType(models.TextChoices):
    """发票目标类型"""

    BILL = "bill", "权责"
    ORDER = "instance", "订单"


class InvoiceRecordState(models.IntegerChoices):
    """开票记录状态"""

    PENDING = 1, "开票中"
    SUCCESS = 2, "开票完成"
    FAIL = 3, "开票失败"


class InvoiceRecordType(models.IntegerChoices):
    """开票类型"""

    PRE_INVOICE = 1, "预开票"
    PRE_BATCH_INVOICE = 2, "批量预开票"


class InvoiceState(models.TextChoices):
    """发票状态"""

    PENDING = "pending", "预开票"
    ISSUED = "issued", "已开票"
    REVERSED = "reversed", "被红冲"
    PAUSED = "paused", "暂不开票"
    CANCELED = "canceled", "不开票"


class InvoiceApprovedState(models.TextChoices):
    """发票审批状态"""

    DRAFT = "draft", "待审批"
    SUBMITTED = "submitted", "审批中"
    APPROVED = "approved", "已审批"
    REJECTED = "rejected", "驳回"


class InvoiceDetailState(models.IntegerChoices):
    """发票明细状态"""

    INVALID = 0, "失效"
    VALID = 1, "有效"


class InvoiceIssuanceAction(models.TextChoices):
    """开票动作"""

    ISSUANCE = "issuance", "开票"
    PAUSE = "pause", "暂不开票"
    CANCEL = "cancel", "不开票"


INVOICE_FILED_MAPPING = {
    "invoice_no": "发票号",
    "amount": "开票金额",
    "account_seq": "分账序号",
    "customer_name": "客户名称",
    "customer_invoice_name": "客户开票名称",
    "invoice_type": "客户发票类型",
    "tax_rate": "税率",
    "tax_amount": "税额",
    "currency_type": "币种",
    "invoice_currency_type": "开票币种",
    "state": "发票状态",
    "exchange_rate": "汇率",
    "signing_entity": "开票实体",
    "customer_num": "客户编号",
    "remark": "备注",
    "create_user": "操作员",
    "created_at": "创建时间",
}


class InvoicePayType(models.IntegerChoices):
    """付费方式"""

    PREPAID = 1, "预付"
    POSTPAID = 2, "后付"


class InvoiceSubmitApproveAction(models.TextChoices):
    """审批动作, 全部、部分"""

    ALL = "all", "全部提交"
    PART = "part", "部分提交"


# 分账序号
# -------------------------------------------------------------------------------------
class AccountSeqTax(models.IntegerChoices):

    ZERO = 0, "0%"
    SIX = 6, "6%"
    NINE = 9, "9%"
    THIRTEEN = 13, "13%"


class InvoiceInfoPriority(models.IntegerChoices):
    """发票信息优先级"""

    LOW = 0, "低"
    HIGH = 1, "高"

