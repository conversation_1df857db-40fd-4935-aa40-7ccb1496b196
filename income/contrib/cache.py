from collections.abc import Awaitable

from django.conf import settings
from redis import StrictRedis


def default_key_func(key, key_prefix, version):
    """
    Default function to generate keys.

    Construct the key used by all other methods. By default, prepend
    the `key_prefix`. KEY_FUNCTION can be used to specify an alternate
    function with custom key making behavior.
    """
    return f"{key_prefix}:{key}"


class CustomRedisClient(StrictRedis):
    @staticmethod
    def add_prefix(arg):
        key_prefix = settings.CACHES["default"]["KEY_PREFIX"]
        if settings.HAS_REDIS_PREFIX:
            if isinstance(arg, dict):
                r = {}
                for k, v in arg.items():
                    r[f"{key_prefix}:{k}"] = v
                    return r
            else:
                return f"{key_prefix}:{arg}"
        return arg

    def _action(self, action_type, *args, **kwargs):
        # scan 命令只有关键字参数 注意match值
        if action_type != "scan":
            args = (self.add_prefix(args[0]), *args[1:])
        method = getattr(super(CustomRedisClient, self), action_type)
        return method(*args, **kwargs)

    def hget(self, *args, **kwargs):
        return self._action("hget", *args, **kwargs)

    def hdel(self, *args, **kwargs):
        return self._action("hdel", *args, **kwargs)

    def hset(self, *args, **kwargs):
        return self._action("hset", *args, **kwargs)

    def hmset(self, name: str, mapping: dict) -> Awaitable[str] | str:
        return self._action("hmset", name, mapping)

    def hsetnx(self, *args, **kwargs):
        return self._action("hsetnx", *args, **kwargs)

    def hincrby(self, *args, **kwargs):
        return self._action("hincrby", *args, **kwargs)

    def incr(self, *args, **kwargs):
        return self._action("incr", *args, **kwargs)

    def scan(self, *args, **kwargs):
        return self._action("scan", *args, **kwargs)

    def scan_iter(self, *args, **kwargs):
        return self._action("scan_iter", *args, **kwargs)

    def smembers(self, *args, **kwargs):
        return self._action("smembers", *args, **kwargs)

    def sismember(self, *args, **kwargs):
        return self._action("sismember", *args, **kwargs)

    def rpush(self, *args, **kwargs):
        return self._action("rpush", *args, **kwargs)

    def sadd(self, *args, **kwargs):
        return self._action("sadd", *args, **kwargs)

    def srem(self, *args, **kwargs):
        return self._action("srem", *args, **kwargs)

    def scard(self, *args, **kwargs):
        return self._action("scard", *args, **kwargs)

    def lpop(self, *args, **kwargs):
        return self._action("lpop", *args, **kwargs)

    def brpop(self, *args, **kwargs):
        return self._action("brpop", *args, **kwargs)

    def rpop(self, *args, **kwargs):
        return self._action("rpop", *args, **kwargs)

    def llen(self, *args, **kwargs):
        return self._action("llen", *args, **kwargs)

    def hgetall(self, *args, **kwargs):
        return self._action("hgetall", *args, **kwargs)

    def set(self, *args, **kwargs):
        return self._action("set", *args, **kwargs)

    def get(self, *args, **kwargs):
        return self._action("get", *args, **kwargs)

    def exists(self, *args, **kwargs):
        return self._action("exists", *args, **kwargs)

    def delete(self, *args, **kwargs):
        return self._action("delete", *args, **kwargs)
