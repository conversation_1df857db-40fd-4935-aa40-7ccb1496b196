from rest_framework import serializers
from rest_framework.utils.field_mapping import get_field_kwargs


class ModelSerializer(serializers.ModelSerializer):
    def build_standard_field(self, field_name, model_field):
        field_kwargs = get_field_kwargs(field_name, model_field)
        if "choices" in field_kwargs:
            # Fields with choices get coerced into `<PERSON>Field`
            # instead of using their regular typed field.
            field_class = self.serializer_choice_field
            # Some model fields may introduce kwargs that would not be valid
            # for the choice field. We need to strip these out.
            valid_kwargs = {
                "read_only",
                "write_only",
                "required",
                "default",
                "initial",
                "source",
                "label",
                "help_text",
                "style",
                "error_messages",
                "validators",
                "allow_null",
                "allow_blank",
                "choices",
            }
            field_kwargs["default"] = field_kwargs["model_field"].default
            for key in list(field_kwargs):
                if key not in valid_kwargs:
                    field_kwargs.pop(key)
        else:
            field_class, field_kwargs = super(
                ModelSerializer,
                self,
            ).build_standard_field(field_name, model_field)
        return field_class, field_kwargs
