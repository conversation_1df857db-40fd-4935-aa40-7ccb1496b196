from django.db import models

from income import const


class CustomerInfo(models.Model):
    """客户信息表"""

    customer_num = models.CharField(
        unique=True,
        max_length=50,
        db_comment="客户编号(唯一标识)",
        error_messages={"unique": "该客户编号已存在,请重新生成"},
    )
    customer_name = models.CharField(max_length=100, db_comment="客户全称")
    customer_name_intl = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="客户原名称,英/法/德/日等",
    )
    group_name = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="客户所属的集团简称,比如:华为、丰田、罗森、阿里巴巴",
    )
    like_key = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="客户关键词(用于模糊查询)",
    )
    customer_usci = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="注册于中国大陆的公司/政府机构/事业单位,它的社会统一信用号码。(仅中国需要)",
    )
    sale_name = models.CharField(max_length=50, db_comment="销售负责人姓名")
    customer_class = models.CharField(
        max_length=20,
        db_comment="客户等级",
        choices=const.CustomerClass.choices,
    )
    customer_type = models.CharField(
        max_length=20,
        db_comment="客户类型",
        choices=const.CustomerType.choices,
    )
    trade_type = models.CharField(
        max_length=20,
        db_comment="行业类型",
        choices=const.TradeType.choices,
    )
    business = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="主营业务",
    )
    remark = models.TextField(blank=True, null=True, db_comment="客户备注")
    upload_file = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="关联文件ID(外键,关联文件表ID)",
    )
    country = models.CharField(max_length=10, blank=True, null=True, db_comment="国家")
    province = models.CharField(max_length=40, blank=True, null=True, db_comment="省份")
    city = models.CharField(max_length=20, blank=True, null=True, db_comment="城市")
    state = models.CharField(
        max_length=10,
        db_comment="状态",
        choices=const.CustomerState.choices,
    )
    approve_state = models.CharField(
        max_length=10,
        db_comment="审核状态",
        choices=const.ApproveState.choices,
        default=const.ApproveState.INIT,
    )
    approve_user = models.CharField(
        max_length=20,
        db_comment="审批人",
        null=True,
        blank=True,
    )
    create_user = models.CharField(max_length=50, db_comment="创建人")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)
    deleted_at = models.DateTimeField(db_comment="删除时间", null=True, blank=True)

    class Meta:
        managed = False
        db_table = "customer_info"
        db_table_comment = "客户信息主表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<CustomerInfo: {self.customer_num}-{self.customer_name}>:"


class ContactInfo(models.Model):
    """客户联系人信息"""

    customer_id = models.IntegerField(db_comment="客户ID(关联客户表ID)")
    name = models.CharField(max_length=50, db_comment="联系人姓名")
    company = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="所属公司",
    )
    position = models.CharField(max_length=50, blank=True, null=True, db_comment="职位")
    address = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="联系地址",
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="固定电话",
    )
    mobile_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="手机号码",
    )
    email = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="电子邮箱",
    )
    contact_class = models.CharField(
        max_length=20,
        db_comment="关联类型(客户、合同、账务)",
        choices=const.ContactClass.choices,
    )
    contact_type = models.CharField(
        max_length=20,
        db_comment="联系人类型(商务;采购;账务;服务;其他)",
        choices=const.ContactType.choices,
    )
    own_type = models.CharField(
        max_length=20,
        choices=const.OwnType.choices,
        blank=True,
        null=True,
        db_comment="归属类型(客户;供应商;合作渠道;)",
    )
    remark = models.TextField(blank=True, null=True, db_comment="备注信息")
    state = models.CharField(
        max_length=1,
        choices=const.State.choices,
        blank=True,
        null=True,
        db_comment="状态(U=有效,E=无效)",
    )
    create_user = models.CharField(max_length=50, db_comment="操作人姓名")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "contact_info"
        db_table_comment = "联系人信息表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<ContactInfo: {self.name}>:"


class CustomerApproveHistory(models.Model):
    """客户审批历史记录"""

    customer_id = models.IntegerField(db_comment="客户ID(关联客户表ID)")
    action = models.CharField(
        max_length=10,
        db_comment="操作类型(submit/revoke/approve/reject)",
    )
    from_state = models.CharField(max_length=10, db_comment="操作前状态")
    to_state = models.CharField(max_length=10, db_comment="操作后状态")
    reason = models.TextField(blank=True, null=True, db_comment="操作原因/意见")
    operator = models.CharField(max_length=50, db_comment="操作人")

    created_at = models.DateTimeField(db_comment="操作时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "customer_approve_history"
        db_table_comment = "客户审批历史记录表"
        ordering = ("created_at",)

    def __str__(self):
        return f"<CustomerApproveHistory: {self.customer_id}-{self.action}>"


class IncomeAccountSeq(models.Model):
    """分账序号管理表"""

    customer_num = models.CharField(max_length=50, db_comment="客户编号")
    customer_name = models.CharField(max_length=255, db_comment="客户名称")
    account_seq = models.CharField(max_length=50, db_comment="分账账号")
    seq_name = models.CharField(max_length=50, db_comment="分账序号名称")
    tax = models.IntegerField(
        choices=const.AccountSeqTax.choices,
        default=const.AccountSeqTax.ZERO,
        db_comment="税率",
    )
    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "income_account_seq"
        db_table_comment = "分账序号管理表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<IncomeAccountSeq: {self.account_seq}>"


class IncomeInvoiceInfo(models.Model):
    """发票信息"""

    customer_invoice_name = models.CharField(max_length=64, db_comment="客户开票名称")
    customer_deposit_bank = models.CharField(
        max_length=128,
        db_comment="客户开户银行",
        default="/",
    )
    customer_deposit_bank_sub = models.CharField(
        max_length=128,
        db_comment="客户开户银行支行",
        default="/",
    )
    customer_bank_account_name = models.CharField(
        max_length=128,
        default="/",
        db_comment="客户银行开户名称",
    )
    customer_bank_account = models.CharField(max_length=128, db_comment="客户银行账号")
    customer_tax_number = models.CharField(max_length=128, db_comment="客户税号")
    customer_invoice_type = models.CharField(
        max_length=32,
        db_comment="客户发票类型",
        choices=const.InvoiceType.choices,
    )
    customer_receive_explain = models.CharField(
        max_length=512,
        blank=True,
        null=True,
        db_comment="客户账单接收说明",
    )
    customer_verify_type = models.CharField(
        max_length=32,
        db_comment="客户对账类型",
        choices=const.CustomerVerifyType.choices,
    )
    customer_fare_order = models.CharField(
        max_length=32,
        db_comment="客户票款先后",
        choices=const.CustomerFareOrder.choices,
    )
    is_unusual_need = models.CharField(
        max_length=32,
        db_comment="是否特殊开账要求",
        choices=const.IsUnusualNeed.choices,
    )
    unusual_need_explain = models.CharField(
        max_length=512,
        blank=True,
        null=True,
        db_comment="特殊开账要求细则",
    )
    is_open_charge = models.CharField(
        max_length=32,
        db_comment="是否开账",
        choices=const.IsOpenCharge.choices,
    )
    postal_type = models.CharField(
        max_length=20,
        db_comment="邮递方式:邮件",
        choices=const.PostalType.choices,
    )
    postal_address = models.CharField(
        max_length=256,
        blank=True,
        null=True,
        db_comment="邮寄地址",
    )
    email_address = models.EmailField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="邮箱地址",
    )
    phone_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="联系电话",
    )
    account_seq = models.CharField(
        max_length=32,
        db_comment="分账序号:系统编号: FZ4位年2位月4位递增数字",
    )
    priority = models.PositiveSmallIntegerField(
        choices=const.InvoiceInfoPriority.choices,
        default=const.InvoiceInfoPriority.HIGH,
        db_comment="优先级: 0:低 1:高(默认1)",
    )

    create_user = models.CharField(max_length=32, db_comment="创建者")
    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_invoice_info"
        db_table_comment = "发票信息"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<IncomeInvoiceInfo: {self.customer_invoice_name}>"
