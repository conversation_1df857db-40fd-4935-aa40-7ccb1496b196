from rest_framework.permissions import BasePermission

from income import message
from income.contrib.drf.shortcuts import get_object_or_404

from .models import CustomerInfo
from .models import IncomeAccountSeq


class CustomerPermission(BasePermission):
    """客户信息权限"""

    def has_permission(self, request, view):
        get_object_or_404(
            CustomerInfo.objects.all(),
            pk=view.kwargs["customer_id"],
            error_message=message.CUSTOMER_NOT_FOUND,
        )
        return True


class AccountSeqPermission(BasePermission):
    """分账序号权限"""

    def has_permission(self, request, view):
        instance = get_object_or_404(
            IncomeAccountSeq.objects.all(),
            pk=view.kwargs["account_seq_id"],
            error_message=message.ACCPUNT_SEQ_NOT_FOUND,
        )
        view.kwargs["account_seq"] = instance.account_seq
        return True
