from django.db import transaction
from rest_framework import serializers

from income import const
from income import message
from income.customer.models import IncomeInvoiceInfo


class InvoiceInfoSerializer(serializers.ModelSerializer):
    """发票信息序列化器"""

    class Meta:
        model = IncomeInvoiceInfo
        exclude = ("updated_at",)
        read_only_fields = ("create_user", "account_seq", "priority")

    @staticmethod
    def judgment_priority(account_seq):
        # 根据分账序号查询发票信息表中是否已经存在高优先级的记录
        if IncomeInvoiceInfo.objects.filter(
            account_seq=account_seq,
            priority=const.InvoiceInfoPriority.HIGH,
        ).exists():
            return const.InvoiceInfoPriority.LOW
        return const.InvoiceInfoPriority.HIGH

    def validate(self, attrs):
        # 根据postal_type判断是否需要填写邮寄地址或者邮箱地址
        postal_type = attrs.get("postal_type")
        if postal_type == const.PostalType.NONE:
            return attrs
        validation_rules = {
            const.PostalType.EMAIL: ["email_address"],
            const.PostalType.EMAIL_AND_EXPRESS: ["email_address", "postal_address"],
            const.PostalType.EXPRESS: ["postal_address"],
        }
        fields_to_validate = validation_rules[postal_type]
        for field in fields_to_validate:
            if not attrs.get(field):
                raise serializers.ValidationError(
                    {field: message.INV_INFO_ERROR_MSG[field]},
                )
        return super().validate(attrs)

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        account_seq = self.context["view"].kwargs["account_seq"]
        validated_data["account_seq"] = account_seq
        validated_data["priority"] = self.judgment_priority(account_seq)
        validated_data["create_user"] = create_user
        return self.Meta.model.objects.create(**validated_data)


class InvoiceInfoSimpleSerializer(serializers.ModelSerializer):
    """发票信息简易序列化器"""

    class Meta:
        model = IncomeInvoiceInfo
        fields = ("id", "customer_invoice_name", "customer_invoice_type")


class AdjustPrioritySerializer(serializers.Serializer):
    """调整优先级序列化器"""

    def validate(self, attrs):
        # 校验当前分账序号下是否只有一条发票信息或者当前发票信息已经是高优先级
        account_seq = self.context["view"].kwargs["account_seq"]
        if (
            IncomeInvoiceInfo.objects.filter(
                account_seq=account_seq,
            ).count()
            == 1
            or self.instance.priority == const.InvoiceInfoPriority.HIGH
        ):
            raise serializers.ValidationError(
                {
                    "priority": message.INV_INFO_NOT_ADJUST_PRIORITY,
                },
            )
        return super().validate(attrs)

    def update(self, instance, validated_data):
         # 将当前的高优先级调整为低优先级
        with transaction.atomic():
            IncomeInvoiceInfo.objects.filter(
                account_seq=instance.account_seq,
                priority=const.InvoiceInfoPriority.HIGH,
            ).update(priority=const.InvoiceInfoPriority.LOW)
            # 将当前的优先级调整为高优先级
            instance.priority = const.InvoiceInfoPriority.HIGH
            instance.save(update_fields=["priority", "updated_at"])
        return {"message": "调整优先级成功"}

    def to_representation(self, data):
        return data
