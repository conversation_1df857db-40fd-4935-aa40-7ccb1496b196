from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import AccountSeqViewSet
from .views import ContactInfoViewSet
from .views import CustomersInfoApproveViewSet
from .views import CustomersInfoViewSet
from .views import InvoiceInfoViewSet
from .views import inv_info_simple_list

router = SimpleRouter(trailing_slash=False)
router.register("customers-info", CustomersInfoViewSet, basename="customers-info")

contact_router = SimpleRouter(trailing_slash=False)
contact_router.register("contacts-info", ContactInfoViewSet, basename="contacts-info")

approve_router = SimpleRouter(trailing_slash=False)
approve_router.register(
    "customers-approve",
    CustomersInfoApproveViewSet,
    basename="customers-approve",
)

account_seq_router = SimpleRouter(trailing_slash=False)
account_seq_router.register(
    "account-seq",
    AccountSeqViewSet,
    basename="account-seq",
)

invoice_info_router = SimpleRouter(trailing_slash=False)
invoice_info_router.register(
    "invoice-info",
    InvoiceInfoViewSet,
    basename="invoice-info",
)


urlpatterns = [
    path("", include(router.urls)),
    path("customers/<int:customer_id>/", include(contact_router.urls)),
    path("", include(approve_router.urls)),
    path("", include(account_seq_router.urls)),
    path("account-seq/<int:account_seq_id>/", include(invoice_info_router.urls)),
    path("invoice-info/simple-list", inv_info_simple_list),
]
