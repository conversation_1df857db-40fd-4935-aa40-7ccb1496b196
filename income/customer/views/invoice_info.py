from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.decorators import api_view
from rest_framework.decorators import permission_classes
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.customer.models import IncomeInvoiceInfo
from income.customer.permissions import AccountSeqPermission
from income.customer.serializers import AdjustPrioritySerializer
from income.customer.serializers import InvoiceInfoSerializer
from income.customer.serializers import InvoiceInfoSimpleSerializer
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission


@extend_schema_view(
    list=extend_schema(summary="获取发票信息"),
    create=extend_schema(summary="新建发票信息"),
    retrieve=extend_schema(summary="获取发票信息详情"),
    adjust_priority=extend_schema(summary="调整发票信息优先级"),
)
@extend_schema(tags=["invoice-info"])
class InvoiceInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
):
    NOT_FOUND_MESSAGE = message.INVOICE_INFO_NOT_FOUND

    serializer_class = InvoiceInfoSerializer
    serializers = {
        "adjust_priority": AdjustPrioritySerializer,
    }
    permission_classes = (IsAuthenticated, AccountSeqPermission, RoleMenuPermission)

    search_fields = [
        "customer_invoice_name",
        "create_user",
    ]
    filter_backends = (SearchAndFilter,)
    search_contains = True

    identify = const.MenuIdentify.ACCOUNT_SEQ

    def get_queryset(self):
        return IncomeInvoiceInfo.objects.filter(
            account_seq=self.kwargs["account_seq"],
        )

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    @extend_schema(
        tags=["invoice-info"],
        responses={200: InvoiceInfoSimpleSerializer(many=True)},
        summary="仅展示发票ID、发票信息名称、发票类型",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset()).values(
            "id",
            "customer_invoice_name",
            "customer_invoice_type",
        )
        return Response(queryset)

    @action(
        methods=["post"],
        detail=True,
        url_path="adjust-priority",
    )
    def adjust_priority(self, request, *args, **kwargs):
        """调整优先级"""
        instance = self.get_object()
        serializer = self.get_serializer(data=request.data, instance=instance)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


@extend_schema(
    tags=["invoice-info"],
    responses={200: InvoiceInfoSimpleSerializer(many=True)},
    summary="仅展示发票ID、发票信息名称、发票类型",
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def inv_info_simple_list(request, *args, **kwargs):
    queryset = IncomeInvoiceInfo.objects.filter(
        account_seq=request.query_params["account_seq"],
    ).values(
        "id",
        "customer_invoice_name",
        "customer_invoice_type",
    )
    return Response(queryset)
