import logging

import pandas as pd

from income import const
from income.charge.models import IncomeChargeDetail

from .models import IncomeInvoicingRecord
from .utils import process_invoice_data

logger = logging.getLogger("invoice")


class InvoiceBatchIssuance(object):  # noqa: UP004
    """
    批量预开票
    开票规则:
        查出来的数据按界⾯条件筛选后,按照分账序号分组,
        累加明细数据⾦额 (权责⾦额-已开票⾦额)

        操作⼊表income_invoice表:
            amount是累加的明细数据⾦额
            tax为分账序号对应的tax
            tax_amount = amount - (amount ÷ (1+tax/100),
            currency_type/invoice_currency_type的值随机挑选⼀条权责的currency_type
            exchange_rate置空,

        操作⼊表income_invoice_detail表:
            invoice_id是上步⼊表的id
            target_id是权责数据的id
            target_type是bill
            amount = 每条的权责⾦额-已开票⾦额
            invoice_month置空
    """

    def __init__(self, data: dict):
        self.task_id = data["task_id"]
        self.start_charge_month = data["start_charge_month"]
        self.end_charge_month = data["end_charge_month"]
        self.customer_num = data.get("customer_num")
        self.account_seq = data.get("account_seq")
        self.create_user = data["create_user"]

    def get_queryset(self):
        sql = """
        SELECT
            a.`id`,
            a.`account_seq`,
            a.`fee_amount`,
            a.`currency_type`,
            a.`customer_num`,
            a.`charge_month`,
            COALESCE(d.`invoice_amount`, 0) AS invoice_amount
        FROM
            `income_charge_detail` a
            LEFT JOIN (
                SELECT
                    `target_id`,
                    sum(`amount`) AS invoice_amount
                FROM
                    `income_invoice_detail`
                WHERE
                    target_type = 'bill'
                    AND state = 1
                GROUP BY
                    `target_id`) d ON a.id = d.`target_id`
        WHERE
            a.`pay_type` = '预付'
            AND (d.invoice_amount IS NULL OR a.`fee_amount` != d.invoice_amount)
            AND a.`need_invoice` = 1
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql)

    def handle_queryset(self, queryset):
        """处理查询结果集,按条件筛选并分组汇总数据"""
        charge_df = pd.DataFrame([row.__dict__ for row in queryset])
        charge_df = charge_df.drop(columns=["_state"])

        # 如果没有数据,直接返回
        if charge_df.empty:
            logger.info("No data found for invoice batch issuance")
            return

        # 1. 对账单周期进行筛选
        charge_df = charge_df[
            (charge_df["charge_month"] >= self.start_charge_month)
            & (charge_df["charge_month"] <= self.end_charge_month)
        ]
        # 2. 如果customer_num有值,先对charge_df筛选
        if self.customer_num:
            charge_df = charge_df[charge_df["customer_num"] == self.customer_num]
        # 3. 如果account_seq有值,同时增加customer_num和account_seq的筛选
        if self.account_seq:
            charge_df = charge_df[charge_df["account_seq"] == self.account_seq]

        # 如果筛选后没有数据,直接返回
        if charge_df.empty:
            logger.info("No data found for invoice batch issuance after filtering")
            return

        charge_df["available_amount"] = (
            charge_df["fee_amount"] - charge_df["invoice_amount"]
        )
        # 过滤掉可开票金额为0或负数的记录
        charge_df = charge_df[charge_df["available_amount"] > 0]
        if charge_df.empty:
            logger.info("No data found for invoice issuance after filtering")
            return

        # 按分账序号分组汇总
        grouped = (
            charge_df.groupby("account_seq")
            .agg(
                {
                    "available_amount": "sum",  # 累加明细数据金额
                    "currency_type": "first",  # 随机挑选一条权责的currency_type
                    "id": list,  # 收集所有相关的权责记录ID
                    "fee_amount": list,  # 收集所有权责金额
                    "invoice_amount": list,  # 收集所有已开票金额
                    "customer_num": "first",  # 收集客户编号
                },
            )
            .reset_index()
        )
        # 批量处理每个分账序号的开票数据
        process_invoice_data(grouped, charge_df, create_user=self.create_user)

    def run(self):
        """执行批量开票任务"""
        queryset = self.get_queryset()
        try:
            self.handle_queryset(queryset)
        except Exception as e:
            logger.exception("Exception occurred during invoice batch issuance")
            IncomeInvoicingRecord.objects.filter(task_id=self.task_id).update(
                state=const.InvoiceRecordState.FAIL,
                reason=str(e),
            )
        else:
            IncomeInvoicingRecord.objects.filter(task_id=self.task_id).update(
                state=const.InvoiceRecordState.SUCCESS,
            )


class InvoiceIssuance(object):  # noqa: UP004
    def __init__(self, data: dict):
        self.task_id = data["task_id"]
        self.charge_detail_ids = data["charge_detail_ids"]
        self.exchange_rate = data["exchange_rate"]
        self.create_user = data["create_user"]
        self.signing_entity = data["signing_entity"]
        self.invoice_currency_type = data["invoice_currency_type"]

    def get_queryset(self):
        sql = """
        SELECT
            a.`id`,
            a.`account_seq`,
            a.`fee_amount`,
            a.`currency_type`,
            a.`customer_num`,
            a.`charge_month`,
            COALESCE(d.`invoice_amount`, 0) AS invoice_amount
        FROM
            `income_charge_detail` a
            LEFT JOIN (
                SELECT
                    `target_id`,
                    sum(`amount`) AS invoice_amount
                FROM
                    `income_invoice_detail`
                WHERE
                    target_type = 'bill'
                    AND state = 1
                GROUP BY
                    `target_id`) d ON a.id = d.`target_id`
        WHERE
            a.`pay_type` = '预付'
            AND (d.invoice_amount IS NULL OR a.`fee_amount` != d.invoice_amount)
            AND a.`need_invoice` = 1
            AND a.id IN %s
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql, [self.charge_detail_ids])

    def handle_queryset(self, queryset):
        """处理查询结果集,按条件筛选并分组汇总数据"""
        charge_df = pd.DataFrame([row.__dict__ for row in queryset])
        charge_df = charge_df.drop(columns=["_state"])

        # 如果没有数据,直接返回
        if charge_df.empty:
            logger.info("No data found for invoice issuance")
            return

        charge_df["available_amount"] = (
            charge_df["fee_amount"] - charge_df["invoice_amount"]
        )
        # 过滤掉可开票金额为0或负数的记录
        charge_df = charge_df[charge_df["available_amount"] > 0]
        if charge_df.empty:
            logger.info("No data found for invoice issuance after filtering")
            return
        # 按照account_seq分组
        grouped = (
            charge_df.groupby("account_seq")
            .agg(
                {
                    "available_amount": "sum",  # 累加明细数据金额
                    "currency_type": "first",  # 随机挑选一条权责的currency_type
                    "id": list,  # 收集所有相关的权责记录ID
                    "fee_amount": list,  # 收集所有权责金额
                    "invoice_amount": list,  # 收集所有已开票金额
                    "customer_num": "first",  # 收集客户编号
                },
            )
            .reset_index()
        )

        process_invoice_data(
            grouped,
            charge_df,
            exchange_rate=self.exchange_rate,
            create_user=self.create_user,
            signing_entity=self.signing_entity,
            invoice_currency_type=self.invoice_currency_type,
        )

    def run(self):
        """执行开票任务"""
        queryset = self.get_queryset()
        try:
            self.handle_queryset(queryset)
        except Exception as e:
            logger.exception("Exception occurred during invoice issuance")
            IncomeInvoicingRecord.objects.filter(task_id=self.task_id).update(
                state=const.InvoiceRecordState.FAIL,
                reason=str(e),
            )
        else:
            IncomeInvoicingRecord.objects.filter(task_id=self.task_id).update(
                state=const.InvoiceRecordState.SUCCESS,
            )
