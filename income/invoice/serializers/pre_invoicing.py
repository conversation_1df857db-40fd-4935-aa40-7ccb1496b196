from decimal import Decimal

from rest_framework import serializers

from income import const
from income import message
from income.invoice.models import IncomeInvoice
from income.invoice.models import IncomeInvoiceDetail
from income.invoice.models import IncomeInvoiceFile
from income.invoice.utils import INVOICE_ISSUANCE_MAP
from income.utils.file_handler import validate_and_save_file


class PreInvoicingSerializer(serializers.ModelSerializer):
    """预开票"""

    customer_name = serializers.CharField(read_only=True, help_text="客户名称")
    customer_invoice_name = serializers.CharField(
        read_only=True,
        help_text="客户开票名称",
    )

    class Meta:
        model = IncomeInvoice
        exclude = (
            "updated_at",
            "group_approved_state",
            "invoice_info_id",
            "customer_num",
        )
        read_only_fields = ("created_at",)


class PreInvoicingUpdateSerializer(serializers.ModelSerializer):
    """预开票的更新信息"""

    tax_rate = serializers.ChoiceField(
        choices=const.AccountSeqTax.choices,
        help_text="税率",
    )

    # 可编辑数据为税率、发票币种、汇率、发票备注
    class Meta:
        model = IncomeInvoice
        fields = (
            "tax_rate",
            "invoice_currency_type",
            "exchange_rate",
            "remark",
        )

    def update(self, instance, validated_data):
        # 如果编辑了税率后, 税额需要重新计算
        new_tax_rate = validated_data.get("tax_rate")
        if instance.tax_rate != new_tax_rate:
            # 计算税额: tax_amount = amount - (amount ÷ (1 + tax/100))
            tax_amount = (
                instance.amount - (instance.amount / Decimal(1 + new_tax_rate / 100))
                if new_tax_rate > 0
                else Decimal("0.00")
            )
            validated_data["tax_amount"] = tax_amount
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save(update_fields=validated_data.keys())
        return instance


class PreInvoicingIssuanceSerializer(serializers.Serializer):
    """预开票的开票信息"""

    invoice_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="发票ID列表",
        required=True,
    )
    action = serializers.ChoiceField(
        help_text="操作类型",
        choices=const.InvoiceIssuanceAction.choices,
        required=True,
    )

    def validate_invoice_ids(self, value):
        # 校验发票信息的状态是否为预开票
        if not IncomeInvoice.objects.filter(
            id__in=value,
            state=const.InvoiceState.PENDING,
        ).exists():
            raise serializers.ValidationError(message.INVOICE_NOT_INVOICING)
        return value

    def create(self, validated_data):
        action = validated_data["action"]
        # 调用开票方法
        INVOICE_ISSUANCE_MAP[action](validated_data)
        # 根据action返回不同的message
        return {"message": message.INVOICE_ISSUANCE_SUCCESS[action]}

    def to_representation(self, data):
        return data


class PreInvoicingUploadAttachmentSerializer(serializers.Serializer):
    """上传附件"""

    file = serializers.FileField(allow_empty_file=False, use_url=False)

    def validate_file(self, value):
        result = validate_and_save_file(
            file=value,
            category="invoice",  # 使用预定义的类别
            allowed_types=["pdf"],  # 只允许PDF
            check_duplicates=False,  # 重复检查
            check_security=True,  # 安全检查
        )
        if not result["success"]:
            # 如果有警告,记录日志
            if result.get("warnings"):
                import logging

                logger = logging.getLogger("file_handler")
                for warning in result["warnings"]:
                    logger.warning(f"文件上传警告: {warning}")  # noqa: G004

            # 使用原有的错误消息格式
            if any("不支持的文件类型" in error for error in result["errors"]):
                raise serializers.ValidationError(message.MULTI_MEDIA_SUFFIX_ERROR)
            if any("文件大小超过限制" in error for error in result["errors"]):
                raise serializers.ValidationError(message.ATTACHMENT_FILE_SIZE_ERROR)
            raise serializers.ValidationError(result["errors"])

        # 存储文件信息供create方法使用
        self.file_info = result["file_info"]
        return value

    def create(self, validated_data):
        """创建文件记录"""
        file_info = getattr(self, "file_info", {})

        # 可以选择将文件信息保存到数据库
        invoice_file = IncomeInvoiceFile.objects.create(
            file_name=file_info.get("original_name"),
            file_path=file_info.get("relative_path"),
            save_file_name=file_info.get("saved_name"),
            create_user=self.context["request"].user.username,
        )
        # 更新发票的invoice_file_id字段
        invoice = self.context["invoice"]
        invoice.invoice_file_id = invoice_file.id
        invoice.save(update_fields=["invoice_file_id", "updated_at"])

        return {"message": "文件上传成功"}

    def to_representation(self, data):
        return data


class PreInvoicingDetailSerializer(serializers.ModelSerializer):
    """预开票详情"""

    # 当target_type为bill时的字段
    sub_order_no = serializers.CharField(read_only=True, help_text="子订单号")
    charge_month = serializers.IntegerField(read_only=True, help_text="账期")
    adjust_month = serializers.IntegerField(read_only=True, help_text="调账月")

    # 当target_type为instance时的字段
    total_num = serializers.CharField(read_only=True, help_text="合成编号")

    class Meta:
        model = IncomeInvoiceDetail
        exclude = ("updated_at", "invoice_id", "state", "target_id")
