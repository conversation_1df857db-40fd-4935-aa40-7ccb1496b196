from django.utils import timezone
from rest_framework import serializers

from income import message
from income.const import InvoiceApprovedState
from income.const import InvoiceSubmitApproveAction
from income.invoice.models import IncomeInvoice


class PreSubmitInvoiceSerializer(serializers.ModelSerializer):
    """预提交发票序列化器"""

    class Meta:
        model = IncomeInvoice
        fields = [
            "id",
            "invoice_no",
            "amount",
            "account_seq",
            "invoice_info_id",
            "invoice_type",
            "tax_rate",
            "tax_amount",
            "currency_type",
            "invoice_currency_type",
            "group_approved_state",
            "exchange_rate",
            "signing_entity",
            "customer_num",
            "remark",
            "invoice_pay_type",
            "create_user",
            "created_at",
        ]


class SubmitApprovalSerializer(serializers.Serializer):
    """提交审批序列化器"""

    ids = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        min_length=1,
        help_text="发票ID列表",
        required=False,
    )
    action = serializers.ChoiceField(
        help_text="操作类型",
        choices=InvoiceSubmitApproveAction.choices,
        required=True,
    )

    def validate(self, attrs):
        ids = attrs.get("ids", [])
        action = attrs.get("action")
        # 如果选择部分提交,必须传入ids && 检查发票是否存在且状态为待审批
        if action == InvoiceSubmitApproveAction.PART:
            if not ids:
                raise serializers.ValidationError({"ids": "请选择要提交的发票"})

            if not IncomeInvoice.objects.filter(
                id__in=ids,
                group_approved_state=InvoiceApprovedState.DRAFT,
            ).exists():
                raise serializers.ValidationError(
                    {"ids": message.INVOICE_NOT_FOUND_OR_INVALID_STATE},
                )

        return super().validate(attrs)

    def create(self, validated_data):
        action = validated_data["action"]
        ids = validated_data.get("ids", [])
        if action == InvoiceSubmitApproveAction.ALL:
            invoices = IncomeInvoice.objects.filter(
                group_approved_state=InvoiceApprovedState.DRAFT,
            )
        else:
            invoices = IncomeInvoice.objects.filter(
                id__in=ids,
                group_approved_state=InvoiceApprovedState.DRAFT,
            )

        # 更新发票状态, 并调用集团审批接口进行审批
        # TODO: 调用集团审批接口进行审批
        invoices.update(
            group_approved_state=InvoiceApprovedState.SUBMITTED,
            updated_at=timezone.now(),
        )

        return {"message": "提交审批成功"}

    def to_representation(self, data):
        return data
