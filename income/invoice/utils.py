import time
from datetime import datetime
from decimal import Decimal

import pandas as pd
from django.db import transaction
from django.db.models.query import RawQuerySet
from django.http import HttpResponse
from django.utils.encoding import escape_uri_path
from utils.excel_export import ExcelExporter

from income import const
from income.charge.models import IncomeChargeDetail
from income.customer.models import CustomerInfo
from income.customer.models import IncomeAccountSeq
from income.customer.models import IncomeInvoiceInfo
from income.utils.common import partial_register

from .models import IncomeInvoice
from .models import IncomeInvoiceDetail


def generate_invoice_no(account_seq: str):
    """生成发票号"""
    # 使用时间戳生成唯一发票号, 确保不超过20个字符
    timestamp = int(time.time() * 1000)
    return f"{account_seq}#{timestamp}"


def get_account_seq_tax_map(grouped_df):
    """获取分账序号对应的税率"""
    res = IncomeAccountSeq.objects.filter(
        account_seq__in=grouped_df["account_seq"],
    ).values("account_seq", "tax")
    return dict(res)


def get_invoice_info(account_seq: str):
    """获取发票信息"""
    return (
        IncomeInvoiceInfo.objects.filter(
            account_seq=account_seq,
            priority=const.InvoiceInfoPriority.HIGH,
        )
        .values_list("id", "customer_invoice_type")
        .last()
    )


def process_invoice_data(grouped_df, original_df, **kwargs):
    """处理发票数据入库

    :param grouped_df: 按照account_seq分组后的数据
    :param original_df: 原始数据
    :param kwargs: 其余的特定数据
    :data kwargs:
        - exchange_rate: 汇率
        - create_user: 创建人
        - signing_entity: 开票实体
        - invoice_currency_type: 开票币种
    :return:
    """
    invoice_objects = []
    invoice_detail_objects = []
    account_seq_tax_map = get_account_seq_tax_map(grouped_df)
    for _, row in grouped_df.iterrows():
        account_seq = row["account_seq"]
        total_amount = row["available_amount"]
        currency_type = row["currency_type"]
        charge_detail_ids = row["id"]
        fee_amounts = row["fee_amount"]
        invoice_amounts = row["invoice_amount"]
        customer_num = row["customer_num"]
        # 获取分账序号对应的税率
        tax_rate = account_seq_tax_map.get(account_seq, 0)
        # 计算税额: tax_amount = amount - (amount ÷ (1 + tax/100))
        tax_amount = (
            total_amount - (total_amount / (1 + tax_rate / 100))
            if tax_rate > 0
            else Decimal("0.00")
        )
        # 获取发票信息
        invoice_info_id, invoice_type = get_invoice_info(account_seq)
        # 创建发票记录
        invoice = IncomeInvoice(
            invoice_no=generate_invoice_no(account_seq),
            amount=total_amount,
            account_seq=account_seq,
            tax_rate=tax_rate,
            tax_amount=tax_amount,
            currency_type=currency_type,
            invoice_currency_type=kwargs.get(
                "invoice_currency_type",
                currency_type,
            ),
            exchange_rate=kwargs.get("exchange_rate"),
            customer_num=customer_num,
            create_user=kwargs.get("create_user"),
            signing_entity=kwargs.get("signing_entity"),
            invoice_info_id=invoice_info_id,
            invoice_type=invoice_type,
        )
        invoice_objects.append(invoice)

        # 为每个权责记录创建发票明细
        for i, charge_detail_id in enumerate(charge_detail_ids):
            detail_amount = fee_amounts[i] - invoice_amounts[i]
            if detail_amount > 0:  # 只处理有可开票金额的记录
                detail = IncomeInvoiceDetail(
                    target_id=charge_detail_id,
                    target_type=const.InvoiceTargetType.BILL,
                    amount=detail_amount,
                )
                invoice_detail_objects.append(detail)

    # 使用事务批量插入数据
    with transaction.atomic():
        # 批量创建发票记录
        IncomeInvoice.objects.bulk_create(
            invoice_objects,
            batch_size=100,
        )

        # 通过发票号查询获取创建的发票ID
        invoice_nos = [invoice.invoice_no for invoice in invoice_objects]
        created_invoices = IncomeInvoice.objects.filter(
            invoice_no__in=invoice_nos,
        ).values("id", "invoice_no", "account_seq")

        # 创建发票号到ID的映射
        invoice_id_mapping = {}
        for invoice_data in created_invoices:
            invoice_id_mapping[invoice_data["account_seq"]] = invoice_data["id"]

        # 设置发票明细的invoice_id
        for detail in invoice_detail_objects:
            # 根据target_id找到对应的account_seq
            charge_detail = original_df[original_df["id"] == detail.target_id]
            if not charge_detail.empty:
                account_seq = charge_detail["account_seq"].iloc[0]
                detail.invoice_id = invoice_id_mapping.get(account_seq)

        # 批量创建发票明细记录
        IncomeInvoiceDetail.objects.bulk_create(
            invoice_detail_objects,
            batch_size=500,
        )


def add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list:
    :return:
    """
    customer_nums = [query_data["customer_num"] for query_data in query_data_list]
    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_nums),
    ).values_list("customer_num", "customer_name")
    customer_map = dict(customer_qs)
    for query_data in query_data_list:
        query_data["customer_name"] = customer_map.get(query_data.pop("customer_num"))
    return query_data_list


def invoice_add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list:
    :return:
    """
    # 获取客户名称
    customer_nums = [query_data["customer_num"] for query_data in query_data_list]
    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_nums),
    ).values_list("customer_num", "customer_name")
    customer_map = dict(customer_qs)
    # 获取发票信息
    invoice_info_ids = [query_data["invoice_info_id"] for query_data in query_data_list]
    invoice_info_qs = IncomeInvoiceInfo.objects.filter(
        id__in=set(invoice_info_ids),
    ).values_list("id", "customer_invoice_name")
    invoice_info_map = dict(invoice_info_qs)
    for query_data in query_data_list:
        query_data["customer_name"] = customer_map.get(query_data.pop("customer_num"))
        query_data["customer_invoice_name"] = invoice_info_map.get(
            query_data.pop("invoice_info_id"),
        )
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list


INVOICE_ISSUANCE_MAP = {}
_register = partial_register(INVOICE_ISSUANCE_MAP)


@_register(const.InvoiceIssuanceAction.ISSUANCE)
def invoice_issuance(data: dict):
    """开票:
        将发票状态更新为已开票

    :param data: 开票数据
    :return:
    """
    invoice_ids = data["invoice_ids"]
    IncomeInvoice.objects.filter(id__in=invoice_ids).update(
        state=const.InvoiceState.ISSUED,
        group_approved_state=const.InvoiceApprovedState.DRAFT,
    )


@_register(const.InvoiceIssuanceAction.PAUSE)
def invoice_pause(data: dict):
    """暂不开票:
        将发票状态更新为暂不开票
        同时将明细状态更新为失效

    :param data: 开票数据
    :return:
    """
    invoice_ids = data["invoice_ids"]
    IncomeInvoice.objects.filter(id__in=invoice_ids).update(
        state=const.InvoiceState.PAUSED,
    )
    IncomeInvoiceDetail.objects.filter(invoice_id__in=invoice_ids).update(
        state=const.InvoiceDetailState.INVALID,
    )


@_register(const.InvoiceIssuanceAction.CANCEL)
def invoice_cancel(data: dict):
    """不开票:
        将发票状态更新为不开票
        同时将明细状态更新为失效
        同时将明细对应的ChargeDetail的need_invoice更新为0

    :param data: 开票数据
    :return:
    """
    invoice_ids = data["invoice_ids"]
    IncomeInvoice.objects.filter(id__in=invoice_ids).update(
        state=const.InvoiceState.CANCELED,
    )
    IncomeInvoiceDetail.objects.filter(invoice_id__in=invoice_ids).update(
        state=const.InvoiceDetailState.INVALID,
    )
    charge_detail_ids = IncomeInvoiceDetail.objects.filter(
        invoice_id__in=invoice_ids,
        target_type=const.InvoiceTargetType.BILL,
    ).values_list("target_id", flat=True)
    # 更新ChargeDetail的need_invoice
    IncomeChargeDetail.objects.filter(id__in=charge_detail_ids).update(
        need_invoice=0,
    )


def generate_file_response(file_data, file_prefix):
    """生成文件响应"""

    # 生成文件名
    filename = f"{file_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    response = HttpResponse(
        content=file_data,
        content_type="application/vnd.ms-excel",
    )
    response["Content-Disposition"] = (
        f'attachment; filename="{escape_uri_path(filename)}"'
    )
    return response


def export_to_excel(queryset: RawQuerySet):
    """导出预开票的数据为Excel文件

    Args:
        queryset (RawQuerySet): 筛选后的数据

    Returns:
        _type_: HttpResponse
    """
    data_list = [row.__dict__ for row in queryset]
    drop_labels = ["_state", "id"]
    # 创建DataFrame
    if not data_list:
        # 如果没有数据,创建空的DataFrame
        pre_invoicing_df = pd.DataFrame(
            columns=list(const.INVOICE_FILED_MAPPING.keys()) + drop_labels,
        )
    else:
        pre_invoicing_df = pd.DataFrame(data_list)
    pre_invoicing_df = pre_invoicing_df.drop(
        labels=["_state", "id"],
        axis=1,
    )
    pre_invoicing_df["state"] = pre_invoicing_df["state"].map(
        dict(const.InvoiceState.choices),
    )
    pre_invoicing_df = pre_invoicing_df.rename(columns=const.INVOICE_FILED_MAPPING)
    # 使用Excel导出工具
    exporter = ExcelExporter()
    excel_file = exporter.export_data(
        data_df=pre_invoicing_df,
        sheet_name="预开票信息",
    )
    return generate_file_response(
        file_data=excel_file,
        file_prefix="预开票信息",
    )


def group_approved_state_export_excel(queryset, file_prefix="待提交"):
    """待提交页面数据导出Excel

    :param queryset: RawQueryset
    :return: HttpResponse
    """

    data_list = [row.__dict__ for row in queryset]
    drop_labels = ["_state", "id"]
    # 表头映射修改
    mapping = const.INVOICE_FILED_MAPPING.copy()
    mapping["group_approved_state"] = "审批状态"
    del mapping["state"]
    # 创建DataFrame
    if not data_list:
        # 如果没有数据,创建空的DataFrame
        pre_submit_df = pd.DataFrame(
            columns=list(mapping.keys()) + drop_labels,
        )
    else:
        pre_submit_df = pd.DataFrame(data_list)
    pre_submit_df = pre_submit_df.drop(
        labels=["_state", "id"],
        axis=1,
    )
    pre_submit_df["group_approved_state"] = pre_submit_df["group_approved_state"].map(
        dict(const.InvoiceApprovedState.choices),
    )
    pre_submit_df = pre_submit_df.rename(columns=mapping)
    # 使用Excel导出工具
    exporter = ExcelExporter()
    excel_file = exporter.export_data(
        data_df=pre_submit_df,
        sheet_name=f"{file_prefix}信息",
    )
    return generate_file_response(
        file_data=excel_file,
        file_prefix=file_prefix,
    )
