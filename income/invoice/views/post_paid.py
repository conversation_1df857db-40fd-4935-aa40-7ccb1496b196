import uuid

from django.conf import settings
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income.charge.filters import IncomeChargeDetailFieldsFilter
from income.charge.models import IncomeChargeDetail
from income.contrib.drf.views import GenericViewSet
from income.invoice.models import IncomeInvoicingRecord
from income.invoice.serializers import InvoiceRecordSerializer
from income.invoice.serializers import PostPaidBatchInvoiceIssuanceSerializer
from income.invoice.serializers import PostPaidInvoiceIssuanceSerializer
from income.invoice.serializers import PostPaidInvoiceSerializer
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission
from pkg.amqp import MQWorker


@extend_schema_view(
    list=extend_schema(summary="获取待开票的权责信息(后付费开票)"),
    batch_issuance=extend_schema(summary="批量开票"),
    issuance=extend_schema(summary="开票"),
)
@extend_schema(tags=["post-paid-invoice"])
class PostPaidInvoiceViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """待开票的权责信息"""

    serializer_class = PostPaidInvoiceSerializer
    serializers = {
        "batch_issuance": PostPaidBatchInvoiceIssuanceSerializer,
        "issuance": PostPaidInvoiceIssuanceSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [IncomeChargeDetailFieldsFilter]
    search_fields = ["sub_order_no", "account_seq", "charge_month", "customer_name"]
    search_contains = True
    table_mapping = {
        "sub_order_no": "a",
        "account_seq": "a",
        "charge_month": "a",
        "customer_name": "b",
    }

    identify = const.MenuIdentify.INVOICE

    def get_queryset(self):
        """
        使用Raw SQL获取完整数据, 减少内存使用
        """
        return self._get_raw_queryset()

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def _get_raw_queryset(self):
        """使用Raw SQL获取完整数据"""
        sql = """
        SELECT
            a.`id`,
            a.`sub_order_no`,
            a.`account_seq`,
            a.`income_type`,
            a.`pay_type`,
            a.`tax`,
            a.`charge_month`,
            a.`fee_amount`,
            b.`customer_name`,
            c.`adjust_month`,
            COALESCE(d.`invoice_amount`, 0) AS invoice_amount
        FROM
            `income_charge_detail` a
            LEFT JOIN `customer_info` b ON a. `customer_num` = b. `customer_num`
            LEFT JOIN `income_adjust_detail` c ON a. `income_adjust_id` = c.id
            LEFT JOIN (
                SELECT
                    `target_id`,
                    sum(`amount`) AS invoice_amount
                FROM
                    `income_invoice_detail`
                WHERE
                    target_type = 'bill'
                    AND state = 1
                GROUP BY
                    `target_id`) d ON a.id = d.`target_id`
        WHERE
            a.`pay_type` = '预付'
            AND (d.invoice_amount IS NULL OR a.`fee_amount` != d.invoice_amount)
            AND a.`need_invoice` = 1
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql)

    @action(
        methods=["post"],
        detail=False,
        url_path="batch-issuance",
    )
    def batch_issuance(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        task_id = str(uuid.uuid4())
        # 创建开票记录
        IncomeInvoicingRecord.objects.create(
            task_id=task_id,
            invoice_type=const.InvoiceRecordType.PRE_BATCH_INVOICE,
            request_info=serializer.data,
            create_user=request.user.username,
        )
        # 发送MQ消息
        with MQWorker(settings.RABBITMQ) as mq_worker:
            mq_worker.publish(
                body={
                    "task_id": task_id,
                    "start_charge_month": serializer.validated_data[
                        "start_charge_month"
                    ],
                    "end_charge_month": serializer.validated_data["end_charge_month"],
                    "customer_num": serializer.validated_data.get("customer_num"),
                    "account_seq": serializer.validated_data.get("account_seq"),
                    "create_user": request.user.username,
                },
                routing_key=settings.BATCH_ISSUANCE_QUEUE,
            )
        return Response({"message": "批量开票成功"})

    @action(
        methods=["post"],
        detail=False,
        url_path="issuance",
    )
    def issuance(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        task_id = str(uuid.uuid4())
        # 创建开票记录
        IncomeInvoicingRecord.objects.create(
            task_id=task_id,
            invoice_type=const.InvoiceRecordType.PRE_INVOICE,
            request_info=serializer.data,
            create_user=request.user.username,
        )
        # 发送MQ消息
        with MQWorker(settings.RABBITMQ) as mq_worker:
            mq_worker.publish(
                body={
                    "task_id": task_id,
                    "charge_detail_ids": serializer.validated_data["charge_detail_ids"],
                    "exchange_rate": serializer.validated_data["exchange_rate"],
                    "signing_entity": serializer.validated_data["signing_entity"],
                    "invoice_currency_type": serializer.validated_data[
                        "invoice_currency_type"
                    ],
                    "create_user": request.user.username,
                },
                routing_key=settings.ISSUANCE_QUEUE,
            )
        return Response({"message": "开票成功"})


@extend_schema_view(
    list=extend_schema(summary="获取开票记录"),
)
@extend_schema(tags=["invoice-record"])
class InvoiceRecordViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    serializer_class = InvoiceRecordSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]

    identify = const.MenuIdentify.INVOICE

    def get_queryset(self):
        return IncomeInvoicingRecord.objects.filter(
            create_user=self.request.user.username,
        )
