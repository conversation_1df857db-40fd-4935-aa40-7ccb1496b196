from rest_framework import serializers

from income.users.serializers.menu import ProfileMenuSerializer


class ProfileSerializer(serializers.Serializer):
    username = serializers.CharField(help_text="用户名")
    email = serializers.EmailField(help_text="邮箱")
    menus = serializers.ListSerializer(
        help_text="菜单列表",
        child=ProfileMenuSerializer(),
    )


class LoginSerializer(serializers.Serializer):
    """登录接口"""

    username = serializers.CharField(help_text="用户名")
    password = serializers.CharField(help_text="密码")


class LogoutSerializer(serializers.Serializer): ...
