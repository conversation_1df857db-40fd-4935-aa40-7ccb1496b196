from rest_framework import serializers

from income.users.models import Menu


class ProfileMenuSerializer(serializers.ModelSerializer):
    children = serializers.ListField(
        help_text="子菜单列表",
        default=[
            {
                "id": 0,
                "menu_name": "子菜单",
                "icon": "pi pi-file",
                "router": "/xxx/xxx",
            },
        ],
        child=serializers.DictField(),
    )

    class Meta:
        model = Menu
        fields = ("id", "menu_name", "icon", "router", "children")
