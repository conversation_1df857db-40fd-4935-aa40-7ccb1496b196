from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income.contrib.drf.filters import SearchSingleFieldFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.permissions import IsAuthenticated
from income.users.models import Role
from income.users.serializers import RoleCreateSerializer
from income.users.serializers import RoleSerializer
from income.users.serializers import RoleSimpleSerializer


@extend_schema_view(
    list=extend_schema(summary="获取角色信息列表"),
    create=extend_schema(summary="新增角色"),
    update=extend_schema(summary="修改角色"),
)
class RoleViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
):

    permission_classes = [IsAuthenticated]
    serializer_class = RoleSerializer
    serializers = {
        "create": RoleCreateSerializer,
        "update": RoleCreateSerializer,
    }

    filter_backends = [SearchSingleFieldFilter]
    search_single_field = "role_name"

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def get_queryset(self):
        return Role.objects.all()

    @extend_schema(
        tags=["roles"],
        responses={200: RoleSimpleSerializer(many=True)},
        summary="仅展示角色ID、角色名称",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.get_queryset().values("id", "role_name")
        return Response(queryset)
