import contextlib
import io

from openpyxl import Workbook
from openpyxl.styles import Alignment
from openpyxl.styles import Border
from openpyxl.styles import Font
from openpyxl.styles import PatternFill
from openpyxl.styles import Side
from openpyxl.utils import get_column_letter
from pandas import DataFrame


class ExcelExporter:
    """Excel导出工具类"""

    def __init__(self):
        self.workbook = Workbook()
        self.worksheet = None

    def export_data(
        self,
        data_df: DataFrame,
        sheet_name: str = "Sheet1",
    ):
        """
        导出数据到Excel

        Args:
            data_df: 要导出的pandas DataFrame数据
            sheet_name: 工作表名称

        Returns:
            Excel文件的二进制数据
        """
        # 获取表头
        headers = data_df.columns

        # 如果数据为空,创建空文件
        if data_df is None or data_df.empty:
            return self._create_empty_file(headers)

        # 创建工作表
        self.worksheet = self.workbook.active
        self.worksheet.title = sheet_name

        # 写入表头
        self._write_headers(headers)

        # 写入数据
        self._write_data(data_df)

        # 设置样式
        self._apply_styles(len(headers), len(data_df))

        # 调整列宽
        self._adjust_column_width()

        # 保存到内存
        return self._save_to_memory()

    def _write_headers(self, headers):
        """写入表头"""
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=1, column=col, value=header)
            # 表头样式
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(
                start_color="366092",
                end_color="366092",
                fill_type="solid",
            )
            cell.alignment = Alignment(horizontal="center", vertical="center")

    def _write_data(self, data_df):
        """写入数据"""
        # 遍历DataFrame的每一行
        for row_idx, (_, row_data) in enumerate(data_df.iterrows(), 2):  # 从第2行开始
            for col_idx, value in enumerate(row_data.values, 1):
                # 处理NaN值,None值
                cell_value = value
                if cell_value is None or (
                    hasattr(cell_value, "isna") and cell_value.isna()
                ):
                    cell_value = ""
                cell = self.worksheet.cell(
                    row=row_idx,
                    column=col_idx,
                    value=cell_value,
                )
                cell.alignment = Alignment(horizontal="left", vertical="center")

    def _apply_styles(self, col_count, row_count):
        """应用样式"""
        # 边框样式
        thin_border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin"),
        )

        # 为所有单元格添加边框
        for row in range(1, row_count + 2):  # +2 因为包含表头
            for col in range(1, col_count + 1):
                cell = self.worksheet.cell(row=row, column=col)
                cell.border = thin_border

        # 数据行交替颜色
        light_fill = PatternFill(
            start_color="F2F2F2",
            end_color="F2F2F2",
            fill_type="solid",
        )
        for row in range(3, row_count + 2, 2):  # 从第3行开始,每隔一行
            for col in range(1, col_count + 1):
                cell = self.worksheet.cell(row=row, column=col)
                cell.fill = light_fill

    def _adjust_column_width(self):
        """调整列宽"""
        for column in self.worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                with contextlib.suppress(Exception):
                    max_length = max(max_length, len(str(cell.value)))

            # 设置列宽,最小10,最大50
            adjusted_width = min(max(max_length + 2, 10), 50)
            self.worksheet.column_dimensions[column_letter].width = adjusted_width

    def _save_to_memory(self):
        """保存到内存"""
        output = io.BytesIO()
        self.workbook.save(output)
        output.seek(0)
        return output.getvalue()

    def _create_empty_file(self, headers):
        """创建空文件"""
        self.worksheet = self.workbook.active
        # 写入表头
        self._write_headers(headers)
        return self._save_to_memory()
