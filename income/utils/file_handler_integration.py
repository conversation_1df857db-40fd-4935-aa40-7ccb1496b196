from rest_framework import serializers
from rest_framework.decorators import action
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response

from income import message
from income.invoice.models import IncomeInvoiceFile
from income.utils.file_handler import FileHandler
from income.utils.file_handler import validate_and_save_file

# ============================================================================
# 1. 改进的预开票附件上传序列化器
# ============================================================================


class ImprovedPreInvoicingUploadAttachmentSerializer(serializers.Serializer):
    """改进的预开票附件上传序列化器 - 使用新的文件工具类"""

    file = serializers.FileField(allow_empty_file=False, use_url=False)

    def validate_file(self, value):
        """使用新的文件工具类进行验证"""
        result = validate_and_save_file(
            file=value,
            category="invoice",  # 使用预定义的类别
            allowed_types=["pdf"],  # 只允许PDF
            max_size=2 * 1024 * 1024,  # 2MB限制
            check_security=True,  # 安全检查
        )

        if not result["success"]:
            # 使用原有的错误消息格式
            if any("不支持的文件类型" in error for error in result["errors"]):
                raise serializers.ValidationError(message.MULTI_MEDIA_SUFFIX_ERROR)
            if any("文件大小超过限制" in error for error in result["errors"]):
                raise serializers.ValidationError(message.ATTACHMENT_FILE_SIZE_ERROR)
            raise serializers.ValidationError(result["errors"][0])

        # 存储文件信息供create方法使用
        self.file_info = result["file_info"]

        # 如果有警告,记录日志
        if result.get("warnings"):
            import logging

            logger = logging.getLogger(__name__)
            for warning in result["warnings"]:
                logger.warning(f"文件上传警告: {warning}")  # noqa: G004

        return value

    def create(self, validated_data):
        """创建文件记录"""
        file_info = getattr(self, "file_info", {})

        # 可以选择将文件信息保存到数据库
        IncomeInvoiceFile.objects.create(
            file_name=file_info.get("original_name"),
            file_path=file_info.get("relative_path"),
            save_file_name=file_info.get("saved_name"),
            create_user=self.context["request"].user.username,
        )

        return {
            "file_path": file_info.get("relative_path"),
            "original_name": file_info.get("original_name"),
            "file_size": file_info.get("file_size"),
            "message": "文件上传成功",
        }


# ============================================================================
# 2. 通用文件上传序列化器
# ============================================================================


class UniversalFileUploadSerializer(serializers.Serializer):
    """通用文件上传序列化器 - 支持多种文件类型和类别"""

    file = serializers.FileField(allow_empty_file=False)
    category = serializers.ChoiceField(
        choices=["customer", "invoice", "order", "contract", "receipt"],
        default="invoice",
    )
    file_types = serializers.ListField(
        child=serializers.ChoiceField(choices=["pdf", "word", "excel", "image"]),
        default=["pdf", "word", "excel", "image"],
    )
    max_size_mb = serializers.IntegerField(default=10, min_value=1, max_value=100)
    subfolder = serializers.CharField(max_length=100, required=False, allow_blank=True)

    def validate(self, attrs):
        """验证文件"""
        file = attrs["file"]
        category = attrs["category"]
        file_types = attrs["file_types"]
        max_size = attrs["max_size_mb"] * 1024 * 1024  # 转换为字节

        result = validate_and_save_file(
            file=file,
            category=category,
            allowed_types=file_types,
            max_size=max_size,
            check_duplicates=True,
            check_security=True,
        )

        if not result["success"]:
            raise serializers.ValidationError({"file": result["errors"]})

        # 存储处理结果
        self.file_info = result["file_info"]
        self.warnings = result.get("warnings", [])

        return attrs

    def create(self, validated_data):
        """创建文件记录"""
        return {
            "file_info": getattr(self, "file_info", {}),
            "warnings": getattr(self, "warnings", []),
            "success": True,
        }


# ============================================================================
# 3. 文件管理工具类
# ============================================================================


class FileManagementService:
    """文件管理服务类 - 封装常用的文件操作"""

    def __init__(self):
        self.handler = FileHandler()

    def upload_customer_file(self, file, subfolder=None):
        """上传客户相关文件"""
        return validate_and_save_file(
            file=file,
            category="customer",
            allowed_types=["pdf", "word", "excel", "image"],
            max_size=10 * 1024 * 1024,
            check_duplicates=True,
            check_security=True,
        )

    def upload_invoice_attachment(self, file):
        """上传发票附件 - 严格限制"""
        return validate_and_save_file(
            file=file,
            category="invoice",
            allowed_types=["pdf"],
            max_size=2 * 1024 * 1024,
            check_duplicates=True,
            check_security=True,
        )

    def upload_contract_document(self, file, contract_id=None):
        """上传合同文档"""
        return validate_and_save_file(
            file=file,
            category="contract",
            allowed_types=["pdf", "word"],
            max_size=20 * 1024 * 1024,
            check_security=True,
        )

    def get_file_info(self, file_path):
        """获取文件信息"""
        return self.handler.get_file_info(file_path)

    def delete_file(self, file_path):
        """删除文件"""
        return self.handler.delete_file(file_path)

    def cleanup_old_files(self, category, days_old=30):
        """清理旧文件"""
        from income.utils.file_handler import FileCleanupManager

        cleanup_manager = FileCleanupManager(self.handler.storage)
        return cleanup_manager.cleanup_old_files(category, days_old)


# ============================================================================
# 4. 视图集成示例
# ============================================================================


class ImprovedFileUploadMixin:
    """改进的文件上传混入类"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_service = FileManagementService()

    @action(
        methods=["post"],
        detail=True,
        url_path="attachment",
        parser_classes=[MultiPartParser],
    )
    def upload_attachment(self, request, *args, **kwargs):
        """通用附件上传接口"""
        # 获取对象实例
        instance = self.get_object()

        # 根据不同的模型类型选择不同的处理方式
        if hasattr(instance, "invoice_type"):
            # 发票相关文件
            serializer = ImprovedPreInvoicingUploadAttachmentSerializer(
                data=request.data, context={"request": request},
            )
        else:
            # 通用文件上传
            serializer = UniversalFileUploadSerializer(
                data=request.data, context={"request": request},
            )

        serializer.is_valid(raise_exception=True)
        result = serializer.save()

        return Response({"success": True, "data": result, "message": "文件上传成功"})

    @action(
        methods=["delete"],
        detail=True,
        url_path="attachment/(?P<file_path>.+)",
    )
    def delete_attachment(self, request, file_path=None, *args, **kwargs):
        """删除附件"""
        if not file_path:
            return Response(
                {"success": False, "message": "文件路径不能为空"}, status=400,
            )

        success = self.file_service.delete_file(file_path)

        if success:
            return Response({"success": True, "message": "文件删除成功"})
        return Response(
            {"success": False, "message": "文件删除失败或文件不存在"}, status=404,
        )


# ============================================================================
# 6. 配置建议
# ============================================================================

"""
在 Django settings 中添加以下配置:

# 文件上传配置
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB

# 自定义文件处理配置
FILE_HANDLER_CONFIG = {
    'DEFAULT_MAX_SIZE': 10 * 1024 * 1024,  # 10MB
    'SECURITY_CHECK_ENABLED': True,
    'DUPLICATE_CHECK_ENABLED': True,
    'AUTO_CLEANUP_ENABLED': True,
    'CLEANUP_DAYS': 30,
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file_handler': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/file_handler.log',
        },
    },
    'loggers': {
        'income.utils.file_handler': {
            'handlers': ['file_handler'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
"""
