import logging

from django.conf import settings

from pkg.amqp import ConsumerConfig
from pkg.amqp import MQWorker

logger = logging.getLogger("invoice")


@MQWorker(
    consumer_config=ConsumerConfig(
        queue=settings.BATCH_ISSUANCE_QUEUE,
        prefetch_count=3,
    ),
    logger_=logger,
)
def main(data):
    """
    :params data:
        {
            "task_id": "6dcf2460-a3df-11eb-9f1e-acde48001122",
            "start_charge_month": 202104,
            "end_charge_month": 202106,
            "customer_num": "KH-********-fVD9p",
            "account_seq": "FZ-**********",
            "create_user": "admin",
        }
    """
    from income.invoice.mq import InvoiceBatchIssuance

    logger.info(f"Received message: {data}")  # noqa: G004
    InvoiceBatchIssuance(data=data).run()
